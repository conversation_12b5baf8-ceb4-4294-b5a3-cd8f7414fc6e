module.exports = {
  project: {
    ios: {},
    android: {},
  },
  dependencies: {
    'react-native-webview': {
      platforms: {
        ios: {
          // Use version 11.17.2 for iOS
          packageImportPath: 'react-native-webview-ios',
        },
        android: {
          // Use version 13.14.2 for Android (current version)
          packageImportPath: 'react-native-webview',
        },
      },
    },
  },
  // dependencies: {
  //   'react-native-video': {
  //     platforms: {
  //       android: {
  //         sourceDir: '../node_modules/react-native-video/android-exoplayer',
  //       },
  //     },
  //   },
  // },
  //   dependencies: {
  //     "react-native-pusher-push-notifications": {
  //         platforms: {
  //             android: null // this skips autolink for android
  //         }
  //     }
  // },

  assets: ['./app/assets/fonts/'],
};
