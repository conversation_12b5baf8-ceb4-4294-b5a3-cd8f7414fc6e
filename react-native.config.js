module.exports = {
  project: {
    ios: {},
    android: {},
  },
  dependencies: {
    'react-native-webview': {
      platforms: {
        ios: null, // Disable for iOS, we'll use react-native-webview-ios
      },
    },
    'react-native-webview-ios': {
      platforms: {
        android: null, // Disable for Android
      },
    },
  },
  // dependencies: {
  //   'react-native-video': {
  //     platforms: {
  //       android: {
  //         sourceDir: '../node_modules/react-native-video/android-exoplayer',
  //       },
  //     },
  //   },
  // },
  //   dependencies: {
  //     "react-native-pusher-push-notifications": {
  //         platforms: {
  //             android: null // this skips autolink for android
  //         }
  //     }
  // },

  assets: ['./app/assets/fonts/'],
};
